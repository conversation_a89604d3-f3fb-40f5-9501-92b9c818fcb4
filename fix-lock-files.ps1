# PowerShell script to fix Terraform lock files for cross-platform compatibility
# This script generates new .terraform.lock.hcl files with checksums for both Windows and Linux

$ErrorActionPreference = "Stop"

# Define the modules that need lock file updates
$modules = @(
    "business_logic_iac\live\aws\dev-us\kms",
    "business_logic_iac\live\aws\dev-us\bucket", 
    "business_logic_iac\live\aws\dev-us\sns-slack-notifications",
    "business_logic_iac\live\aws\dev-us\business-logic",
    "business_logic_iac\live\aws\qa-us\kms",
    "business_logic_iac\live\aws\qa-us\bucket",
    "business_logic_iac\live\aws\qa-us\sns-slack-notifications", 
    "business_logic_iac\live\aws\qa-us\business-logic",
    "business_logic_iac\live\aws\prod-us\kms",
    "business_logic_iac\live\aws\prod-us\bucket",
    "business_logic_iac\live\aws\prod-us\sns-slack-notifications",
    "business_logic_iac\live\aws\prod-us\business-logic",
    "business_logic_iac\live\aws\prod-ca\kms",
    "business_logic_iac\live\aws\prod-ca\bucket", 
    "business_logic_iac\live\aws\prod-ca\sns-slack-notifications",
    "business_logic_iac\live\aws\prod-ca\business-logic"
)

Write-Host "Starting Terraform lock file fix process..." -ForegroundColor Green

foreach ($module in $modules) {
    Write-Host "Processing module: $module" -ForegroundColor Yellow
    
    if (Test-Path $module) {
        Push-Location $module
        
        try {
            # Remove existing lock file if it exists
            if (Test-Path ".terraform.lock.hcl") {
                Write-Host "  Removing existing lock file..." -ForegroundColor Cyan
                Remove-Item ".terraform.lock.hcl" -Force
            }
            
            # Remove .terraform directory if it exists to ensure clean state
            if (Test-Path ".terraform") {
                Write-Host "  Removing .terraform directory..." -ForegroundColor Cyan
                Remove-Item ".terraform" -Recurse -Force
            }
            
            # Initialize terraform to download providers
            Write-Host "  Running terraform init..." -ForegroundColor Cyan
            terragrunt init --terragrunt-non-interactive
            
            # Generate cross-platform lock file
            Write-Host "  Generating cross-platform lock file..." -ForegroundColor Cyan
            terraform providers lock -platform=windows_amd64 -platform=linux_amd64 -platform=darwin_amd64
            
            Write-Host "  ✓ Successfully updated lock file for $module" -ForegroundColor Green
        }
        catch {
            Write-Host "  ✗ Error processing $module : $_" -ForegroundColor Red
        }
        finally {
            Pop-Location
        }
    }
    else {
        Write-Host "  ⚠ Module path does not exist: $module" -ForegroundColor Yellow
    }
}

Write-Host "Lock file fix process completed!" -ForegroundColor Green
Write-Host "Please commit the updated .terraform.lock.hcl files to your repository." -ForegroundColor Cyan
