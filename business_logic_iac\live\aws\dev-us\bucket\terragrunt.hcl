dependency "kms" {
  config_path = "../kms"
  mock_outputs_allowed_terraform_commands = ["validate", "plan", "show"]
  mock_outputs = {
    kms_key_arns = {
      "washingtoncounty-mn" = "arn:aws:kms:us-east-1:123456789012:key/washingtoncounty-mn-key-id"
      "flagler" = "arn:aws:kms:us-east-1:123456789012:key/flagler-key-id"
      "pvgt" = "arn:aws:kms:us-east-1:123456789012:key/pvgt-key-id"
    }
    kms_key_ids = {
      "washingtoncounty-mn" = "washingtoncounty-mn-key-id"
      "flagler" = "flagler-key-id"
      "pvgt" = "pvgt-key-id"
    }
    kms_key_aliases = {
      "washingtoncounty-mn" = "alias/dev-us-smartanalytics-washingtoncounty-mn-key"
      "flagler" = "alias/dev-us-smartanalytics-flagler-key"
      "pvgt" = "alias/dev-us-smartanalytics-pvgt-key"
    }
  }
}


inputs = {
  aws_region = "us-east-1"
  environment = "dev"
  country = "us"
  bucket_name = "lambdas"
  customer_config = {
    "washingtoncounty-mn" = {
      enabled = true
      kms_key_arn = dependency.kms.outputs.kms_key_arns["washingtoncounty-mn"]
      customer_name = "washingtoncounty-mn"
      tags = {
        approval_date = "08/15/2025"
        billing       = "cust_washingtoncounty_mn_dev"
        ChargeCode    = "03SMAL.GENN.0000.WA9.DEV"
        customer      = "washingtoncounty_mn"
      }
    }
    "flagler" = {
      enabled = true
      kms_key_arn = dependency.kms.outputs.kms_key_arns["flagler"]
      customer_name = "flagler"
      tags = {
        approval_date = "08/15/2025"
        billing       = "cust_flagler_fl_dev"
        ChargeCode    = "03SMAL.GENN.0000.WA9.DEV"
        customer      = "flagler_fl"
      }
    }
    "pvgt" = {
      enabled = true
      kms_key_arn = dependency.kms.outputs.kms_key_arns["pvgt"]
      customer_name = "pvgt"
      tags = {
        approval_date = "08/15/2025"
        billing       = "cust_pvgt_dev"
        ChargeCode    = "03SMAL.GENN.0000.WA9.DEV"
        customer      = "pvgt"
      }
    }
  }

  s3_lifecycle_noncurrent_version_expiration_days = 30

  tags = merge(
    include.root.locals.tags,
    {
      country     = "us"
      environment = "dev"
      Module      = "bucket"
    }
  )
}

terraform {
  source = "../../../../modules/bucket/"
}

include "root" {
  expose = true
  path   = find_in_parent_folders()
}
